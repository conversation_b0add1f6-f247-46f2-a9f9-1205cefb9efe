<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NeuroScan - Analyse de tumeurs cérébrales</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'inter': ['Inter', 'sans-serif'],
                    },
                    colors: {
                        'primary': {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            900: '#1e3a8a',
                        },
                        'medical': {
                            50: '#f0f9ff',
                            100: '#e0f2fe',
                            500: '#0ea5e9',
                            600: '#0284c7',
                            700: '#0369a1',
                        }
                    },
                    animation: {
                        'fade-in': 'fadeIn 0.5s ease-in-out',
                        'slide-up': 'slideUp 0.5s ease-out',
                        'pulse-slow': 'pulse 3s infinite',
                        'bounce-slow': 'bounce 2s infinite',
                    }
                }
            }
        }
    </script>
    <style>
        body {
            font-family: 'Inter', sans-serif;
        }

        .upload-area {
            border: 2px dashed #3b82f6;
            transition: all 0.3s ease;
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        }
        .upload-area:hover {
            border-color: #2563eb;
            background: linear-gradient(135deg, #e0f2fe 0%, #f0f9ff 100%);
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(59, 130, 246, 0.15);
        }
        .upload-area.dragover {
            border-color: #10b981;
            background: linear-gradient(135deg, #ecfdf5 0%, #f0fdf4 100%);
            transform: scale(1.02);
        }

        .progress-bar {
            height: 8px;
            transition: width 0.3s ease;
            background: linear-gradient(90deg, #3b82f6, #1d4ed8);
            border-radius: 4px;
        }



        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideUp {
            from { transform: translateY(30px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }

        #resultImage {
            max-height: 500px;
            object-fit: contain;
            border-radius: 12px;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
        }

        .diagnosis-badge {
            padding: 0.75rem 1.5rem;
            border-radius: 50px;
            font-weight: 600;
            font-size: 0.875rem;
            letter-spacing: 0.025em;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .card-hover {
            transition: all 0.3s ease;
        }
        .card-hover:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .medical-gradient {
            background: linear-gradient(135deg, #0ea5e9 0%, #0369a1 100%);
        }

        .glass-effect {
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.9);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .section-divider {
            height: 1px;
            background: linear-gradient(90deg, transparent, #e5e7eb, transparent);
        }

        .feature-icon {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .stats-number {
            background: linear-gradient(135deg, #10b981, #059669);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .btn-primary {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            background: linear-gradient(135deg, #2563eb, #1e40af);
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(59, 130, 246, 0.3);
        }

        .navbar-blur {
            backdrop-filter: blur(20px);
            background: rgba(255, 255, 255, 0.95);
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }

        .hero-pattern {
            background-image: radial-gradient(circle at 1px 1px, rgba(59, 130, 246, 0.15) 1px, transparent 0);
            background-size: 20px 20px;
        }
    </style>
</head>
<body class="bg-gray-50 font-sans">
    <!-- Header -->
    <header class="navbar-blur fixed w-full top-0 z-50 transition-all duration-300">
        <div class="container mx-auto px-4 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 medical-gradient rounded-xl flex items-center justify-center">
                        <i class="fas fa-brain text-white text-lg"></i>
                    </div>
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900">NeuroScan</h1>
                        <p class="text-xs text-gray-500 font-medium">AI Medical Analysis</p>
                    </div>
                </div>

                <nav class="hidden lg:flex items-center space-x-8">
                    <a href="#home" class="text-primary-600 font-semibold border-b-2 border-primary-600 pb-1">Accueil</a>
                    <a href="#features" class="text-gray-600 hover:text-primary-600 font-medium transition-colors">Fonctionnalités</a>
                    <a href="#how-it-works" class="text-gray-600 hover:text-primary-600 font-medium transition-colors">Comment ça marche</a>
                    <a href="#stats" class="text-gray-600 hover:text-primary-600 font-medium transition-colors">Statistiques</a>
                    <a href="#contact" class="text-gray-600 hover:text-primary-600 font-medium transition-colors">Contact</a>
                </nav>

                <div class="flex items-center space-x-4">
                    <a href="/tumor-tracking" class="hidden md:block text-purple-600 hover:text-purple-800 font-medium transition-colors">
                        <i class="fas fa-chart-line mr-2"></i>Suivi Tumeurs
                    </a>
                    <a href="/pro-dashboard" class="hidden md:block btn-primary text-white px-6 py-2 rounded-full font-medium hover:shadow-lg transition-all">
                        <i class="fas fa-chart-bar mr-2"></i>Espace Pro
                    </a>
                    <button id="mobileMenuBtn" class="lg:hidden text-gray-600 hover:text-primary-600">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Mobile Menu -->
        <div id="mobileMenu" class="lg:hidden hidden bg-white border-t border-gray-100">
            <div class="container mx-auto px-4 py-4 space-y-3">
                <a href="#home" class="block text-primary-600 font-semibold">Accueil</a>
                <a href="#features" class="block text-gray-600 hover:text-primary-600">Fonctionnalités</a>
                <a href="#how-it-works" class="block text-gray-600 hover:text-primary-600">Comment ça marche</a>
                <a href="#stats" class="block text-gray-600 hover:text-primary-600">Statistiques</a>
                <a href="#contact" class="block text-gray-600 hover:text-primary-600">Contact</a>
                <a href="/tumor-tracking" class="block text-purple-600 hover:text-purple-800 font-medium">
                    <i class="fas fa-chart-line mr-2"></i>Suivi Tumeurs
                </a>
                <a href="/pro-dashboard" class="block w-full btn-primary text-white px-6 py-3 rounded-full font-medium mt-4 text-center">
                    <i class="fas fa-chart-bar mr-2"></i>Espace Pro
                </a>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="pt-20">
        <!-- Hero Section -->
        <section id="home" class="hero-pattern bg-gradient-to-br from-primary-50 via-white to-medical-50 py-20 lg:py-32">
            <div class="container mx-auto px-4 lg:px-8">
                <div class="max-w-6xl mx-auto">
                    <div class="text-center mb-16">
                        <div class="inline-flex items-center bg-primary-100 text-primary-700 px-4 py-2 rounded-full text-sm font-semibold mb-6">
                            <i class="fas fa-sparkles mr-2"></i>
                            Technologie IA de pointe
                        </div>
                        <h2 class="text-5xl lg:text-7xl font-bold text-gray-900 mb-6 leading-tight">
                            Analyse IA de<br>
                            <span class="bg-gradient-to-r from-primary-600 to-medical-600 bg-clip-text text-transparent">
                                Tumeurs Cérébrales
                            </span>
                        </h2>
                        <p class="text-xl lg:text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed mb-8">
                            Révolutionnez le diagnostic médical avec notre technologie d'intelligence artificielle avancée.
                            Analysez les images IRM et détectez les anomalies cérébrales avec une précision exceptionnelle
                            en quelques secondes.
                        </p>
                        <div class="flex flex-col sm:flex-row gap-4 justify-center items-center">
                            <button onclick="document.getElementById('upload-section').scrollIntoView({behavior: 'smooth'})"
                                    class="btn-primary text-white px-8 py-4 rounded-full font-semibold text-lg">
                                <i class="fas fa-upload mr-2"></i>Commencer l'analyse
                            </button>
                            <button class="bg-white text-gray-700 px-8 py-4 rounded-full font-semibold text-lg border-2 border-gray-200 hover:border-primary-300 transition-all">
                                <i class="fas fa-play mr-2"></i>Voir la démo
                            </button>
                        </div>
                    </div>

                    <!-- Stats Cards -->
                    <div class="grid grid-cols-2 lg:grid-cols-4 gap-6 mt-16">
                        <div class="glass-effect rounded-2xl p-6 text-center card-hover">
                            <div class="stats-number text-3xl font-bold mb-2">99.2%</div>
                            <div class="text-gray-600 font-medium">Précision</div>
                        </div>
                        <div class="glass-effect rounded-2xl p-6 text-center card-hover">
                            <div class="stats-number text-3xl font-bold mb-2">&lt;30s</div>
                            <div class="text-gray-600 font-medium">Temps d'analyse</div>
                        </div>
                        <div class="glass-effect rounded-2xl p-6 text-center card-hover">
                            <div class="stats-number text-3xl font-bold mb-2">50K+</div>
                            <div class="text-gray-600 font-medium">Analyses réalisées</div>
                        </div>
                        <div class="glass-effect rounded-2xl p-6 text-center card-hover">
                            <div class="stats-number text-3xl font-bold mb-2">4</div>
                            <div class="text-gray-600 font-medium">Types de tumeurs</div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Features Section -->
        <section id="features" class="py-20 bg-white">
            <div class="container mx-auto px-4 lg:px-8">
                <div class="text-center mb-16">
                    <h3 class="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
                        Fonctionnalités avancées
                    </h3>
                    <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                        Notre plateforme combine l'expertise médicale et l'intelligence artificielle
                        pour offrir des analyses précises et fiables.
                    </p>
                </div>

                <div class="grid lg:grid-cols-3 gap-8">
                    <div class="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-3xl p-8 card-hover">
                        <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center mb-6">
                            <i class="fas fa-brain text-white text-2xl"></i>
                        </div>
                        <h4 class="text-2xl font-bold text-gray-900 mb-4">IA Avancée</h4>
                        <p class="text-gray-600 leading-relaxed">
                            Modèle CNN entraîné sur des milliers d'images IRM pour une détection précise
                            des anomalies cérébrales.
                        </p>
                    </div>

                    <div class="bg-gradient-to-br from-emerald-50 to-teal-50 rounded-3xl p-8 card-hover">
                        <div class="w-16 h-16 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-2xl flex items-center justify-center mb-6">
                            <i class="fas fa-clock text-white text-2xl"></i>
                        </div>
                        <h4 class="text-2xl font-bold text-gray-900 mb-4">Analyse Rapide</h4>
                        <p class="text-gray-600 leading-relaxed">
                            Obtenez des résultats détaillés en moins de 30 secondes avec des recommandations
                            cliniques personnalisées.
                        </p>
                    </div>

                    <div class="bg-gradient-to-br from-purple-50 to-pink-50 rounded-3xl p-8 card-hover">
                        <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-600 rounded-2xl flex items-center justify-center mb-6">
                            <i class="fas fa-shield-alt text-white text-2xl"></i>
                        </div>
                        <h4 class="text-2xl font-bold text-gray-900 mb-4">Sécurisé</h4>
                        <p class="text-gray-600 leading-relaxed">
                            Vos données sont protégées avec un chiffrement de niveau médical et
                            supprimées automatiquement après analyse.
                        </p>
                    </div>
                </div>
            </div>
        </section>

        <!-- How It Works Section -->
        <section id="how-it-works" class="py-20 bg-gray-50">
            <div class="container mx-auto px-4 lg:px-8">
                <div class="text-center mb-16">
                    <h3 class="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
                        Comment ça marche
                    </h3>
                    <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                        Un processus simple et efficace en 3 étapes pour obtenir une analyse complète
                    </p>
                </div>

                <div class="grid lg:grid-cols-3 gap-8 mb-16">
                    <div class="text-center">
                        <div class="w-20 h-20 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-6">
                            <span class="text-white text-2xl font-bold">1</span>
                        </div>
                        <h4 class="text-2xl font-bold text-gray-900 mb-4">Upload</h4>
                        <p class="text-gray-600">Téléchargez votre image IRM au format DICOM, NIfTI, JPEG ou PNG</p>
                    </div>

                    <div class="text-center">
                        <div class="w-20 h-20 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-full flex items-center justify-center mx-auto mb-6">
                            <span class="text-white text-2xl font-bold">2</span>
                        </div>
                        <h4 class="text-2xl font-bold text-gray-900 mb-4">Analyse</h4>
                        <p class="text-gray-600">Notre IA analyse l'image et détecte les anomalies potentielles</p>
                    </div>

                    <div class="text-center">
                        <div class="w-20 h-20 bg-gradient-to-br from-purple-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-6">
                            <span class="text-white text-2xl font-bold">3</span>
                        </div>
                        <h4 class="text-2xl font-bold text-gray-900 mb-4">Résultats</h4>
                        <p class="text-gray-600">Recevez un rapport détaillé avec recommandations cliniques</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Upload & Analysis Section -->
        <section id="upload-section" class="py-20 bg-white">
            <div class="container mx-auto px-4 lg:px-8">
                <div class="max-w-6xl mx-auto">
                    <div class="text-center mb-12">
                        <h3 class="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
                            Commencez votre analyse
                        </h3>
                        <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                            Téléchargez votre image IRM et obtenez une analyse détaillée en quelques secondes
                        </p>
                    </div>

                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
                        <!-- Upload Card -->
                        <div class="bg-gradient-to-br from-white to-gray-50 rounded-3xl shadow-xl border border-gray-100 overflow-hidden card-hover">
                            <div class="bg-gradient-to-r from-primary-500 to-medical-500 p-6">
                                <h3 class="text-2xl font-bold text-white flex items-center">
                                    <i class="fas fa-upload mr-3"></i>
                                    Upload d'image IRM
                                </h3>
                                <p class="text-primary-100 mt-2">Formats supportés: DICOM, NIfTI, JPEG, PNG</p>
                            </div>
                            <div class="p-8">
                                <div id="uploadArea" class="upload-area rounded-2xl p-12 text-center cursor-pointer transition-all duration-300">
                                    <div class="mb-6">
                                        <i class="fas fa-cloud-upload-alt text-6xl text-primary-500 mb-4"></i>
                                        <div class="w-16 h-1 bg-gradient-to-r from-primary-500 to-medical-500 mx-auto rounded-full"></div>
                                    </div>
                                    <p class="text-xl font-semibold text-gray-700 mb-3">Glissez votre image IRM ici</p>
                                    <p class="text-gray-500 mb-6">ou cliquez pour sélectionner un fichier</p>
                                    <div class="inline-flex items-center bg-primary-50 text-primary-700 px-4 py-2 rounded-full text-sm font-medium">
                                        <i class="fas fa-info-circle mr-2"></i>
                                        Taille max: 16MB
                                    </div>
                                    <input type="file" id="fileInput" class="hidden" accept=".dcm,.nii,.jpg,.jpeg,.png">
                                </div>

                                <!-- File Info -->
                                <div id="fileInfo" class="hidden mt-6 p-6 bg-gradient-to-r from-green-50 to-emerald-50 rounded-2xl border border-green-200">
                                    <div class="flex items-center justify-between mb-3">
                                        <span class="text-lg font-semibold text-gray-700">Fichier sélectionné:</span>
                                        <i class="fas fa-check-circle text-green-500 text-xl"></i>
                                    </div>
                                    <p id="fileName" class="text-gray-600 mb-4 font-medium"></p>
                                    <div class="w-full bg-gray-200 rounded-full h-3 overflow-hidden">
                                        <div id="uploadProgress" class="progress-bar h-3 rounded-full transition-all duration-500" style="width: 0%"></div>
                                    </div>
                                </div>

                                <!-- Patient Information (Optional) -->
                                <div id="patientInfo" class="hidden mt-6 p-6 bg-gradient-to-r from-purple-50 to-pink-50 rounded-2xl border border-purple-200">
                                    <div class="flex items-center justify-between mb-4">
                                        <span class="text-lg font-semibold text-gray-700">Informations Patient (Optionnel)</span>
                                        <i class="fas fa-user-md text-purple-500 text-xl"></i>
                                    </div>
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div>
                                            <label for="patientId" class="block text-sm font-medium text-gray-700 mb-2">ID Patient</label>
                                            <input type="text" id="patientId" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent" placeholder="P001, P002...">
                                        </div>
                                        <div>
                                            <label for="patientName" class="block text-sm font-medium text-gray-700 mb-2">Nom du Patient</label>
                                            <input type="text" id="patientName" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent" placeholder="Nom complet">
                                        </div>
                                        <div>
                                            <label for="examDate" class="block text-sm font-medium text-gray-700 mb-2">Date d'Examen</label>
                                            <input type="date" id="examDate" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent">
                                        </div>
                                        <div class="flex items-end">
                                            <button type="button" onclick="togglePatientInfo()" class="text-sm text-purple-600 hover:text-purple-800 font-medium">
                                                <i class="fas fa-eye-slash mr-1"></i>Masquer
                                            </button>
                                        </div>
                                    </div>
                                    <div class="mt-3 text-xs text-gray-500">
                                        <i class="fas fa-info-circle mr-1"></i>
                                        Ces informations permettent le suivi de l'évolution des tumeurs dans le temps.
                                    </div>
                                </div>

                                <div class="mt-6 text-center">
                                    <button type="button" id="showPatientInfoBtn" onclick="togglePatientInfo()" class="text-sm text-purple-600 hover:text-purple-800 font-medium">
                                        <i class="fas fa-user-plus mr-1"></i>Ajouter informations patient (suivi évolution)
                                    </button>
                                </div>

                                <button id="analyzeBtn" class="w-full mt-8 btn-primary text-white py-4 px-8 rounded-2xl font-semibold text-lg transition-all duration-300 disabled:bg-gray-400 disabled:cursor-not-allowed disabled:transform-none" disabled>
                                    <i class="fas fa-microscope mr-3"></i>
                                    Lancer l'analyse IA
                                </button>
                            </div>
                        </div>

                        <!-- Analysis Progress Card -->
                        <div id="progressCard" class="bg-gradient-to-br from-white to-blue-50 rounded-3xl shadow-xl border border-blue-100 overflow-hidden hidden card-hover">
                            <div class="bg-gradient-to-r from-blue-500 to-indigo-500 p-6">
                                <h3 class="text-2xl font-bold text-white flex items-center">
                                    <i class="fas fa-cog fa-spin mr-3"></i>
                                    Analyse en cours
                                </h3>
                                <p class="text-blue-100 mt-2">Intelligence artificielle au travail...</p>
                            </div>
                            <div class="p-8">
                                <div class="mb-8">
                                    <div class="flex justify-between mb-3">
                                        <span class="text-lg font-semibold text-gray-700">Progression de l'analyse</span>
                                        <span id="progressPercent" class="text-lg font-bold text-blue-600">0%</span>
                                    </div>
                                    <div class="w-full bg-gray-200 rounded-full h-4 overflow-hidden">
                                        <div id="analysisProgress" class="progress-bar h-4 rounded-full transition-all duration-500" style="width: 0%"></div>
                                    </div>
                                </div>
                    
                                <div class="space-y-6">
                                    <div class="flex items-center p-4 bg-green-50 rounded-xl border border-green-200">
                                        <div class="flex-shrink-0 h-12 w-12 rounded-full bg-green-100 flex items-center justify-center mr-4">
                                            <i class="fas fa-check text-green-600"></i>
                                        </div>
                                        <div>
                                            <p class="font-semibold text-gray-800">Pré-traitement de l'image</p>
                                            <p class="text-sm text-gray-600">Normalisation et amélioration du contraste</p>
                                        </div>
                                    </div>

                                    <div class="flex items-center p-4 bg-blue-50 rounded-xl border border-blue-200">
                                        <div class="flex-shrink-0 h-12 w-12 rounded-full bg-blue-100 flex items-center justify-center mr-4">
                                            <i id="segmentationIcon" class="fas fa-spinner fa-spin text-blue-600"></i>
                                        </div>
                                        <div>
                                            <p class="font-semibold text-gray-800">Segmentation des tissus</p>
                                            <p class="text-sm text-gray-600">Identification des régions d'intérêt</p>
                                        </div>
                                    </div>

                                    <div class="flex items-center p-4 bg-gray-50 rounded-xl border border-gray-200">
                                        <div class="flex-shrink-0 h-12 w-12 rounded-full bg-gray-100 flex items-center justify-center mr-4">
                                            <i id="classificationIcon" class="fas fa-circle text-gray-400"></i>
                                        </div>
                                        <div>
                                            <p class="font-semibold text-gray-500">Classification de la tumeur</p>
                                            <p class="text-sm text-gray-400">Analyse des caractéristiques morphologiques</p>
                                        </div>
                                    </div>
                                </div>

                                <!-- Description médicale par IA -->
                                <div id="medicalDescription" class="bg-gradient-to-br from-white to-indigo-50 border border-indigo-200 rounded-2xl p-6 shadow-lg">
                                    <h4 class="text-xl font-bold text-gray-800 mb-4 flex items-center">
                                        <i class="fas fa-brain text-indigo-500 mr-3"></i>
                                        Analyse médicale détaillée
                                    </h4>
                                    <div class="p-4 bg-white rounded-xl border border-indigo-100">
                                        <p id="aiDescription" class="text-gray-700 leading-relaxed">
                                            Analyse en cours par l'intelligence artificielle...
                                        </p>
                                    </div>
                                    <div class="mt-4 text-xs text-gray-500 flex items-center">
                                        <i class="fas fa-robot mr-2"></i>
                                        Généré par Gemini AI - Assistant médical
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Statistics Section -->
        <section id="stats" class="py-20 bg-gradient-to-br from-gray-900 via-blue-900 to-indigo-900 text-white">
            <div class="container mx-auto px-4 lg:px-8">
                <div class="text-center mb-16">
                    <h3 class="text-4xl lg:text-5xl font-bold mb-6">
                        Performances exceptionnelles
                    </h3>
                    <p class="text-xl text-gray-300 max-w-3xl mx-auto">
                        Notre technologie d'IA a été validée sur des milliers de cas cliniques
                    </p>
                </div>

                <div class="grid grid-cols-2 lg:grid-cols-4 gap-8">
                    <div class="text-center">
                        <div class="text-5xl lg:text-6xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-cyan-400 mb-4">
                            99.2%
                        </div>
                        <div class="text-xl font-semibold mb-2">Précision</div>
                        <div class="text-gray-400">Détection des anomalies</div>
                    </div>

                    <div class="text-center">
                        <div class="text-5xl lg:text-6xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-emerald-400 to-teal-400 mb-4">
                            &lt;30s
                        </div>
                        <div class="text-xl font-semibold mb-2">Temps d'analyse</div>
                        <div class="text-gray-400">Résultats instantanés</div>
                    </div>

                    <div class="text-center">
                        <div class="text-5xl lg:text-6xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-pink-400 mb-4">
                            50K+
                        </div>
                        <div class="text-xl font-semibold mb-2">Analyses</div>
                        <div class="text-gray-400">Cas traités avec succès</div>
                    </div>

                    <div class="text-center">
                        <div class="text-5xl lg:text-6xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-orange-400 to-red-400 mb-4">
                            4
                        </div>
                        <div class="text-xl font-semibold mb-2">Types de tumeurs</div>
                        <div class="text-gray-400">Classification complète</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Results Section -->
        <section class="py-20 bg-gray-50">
            <div class="container mx-auto px-4 lg:px-8">
                <!-- Results Card -->
                <div id="resultsCard" class="max-w-6xl mx-auto bg-white rounded-3xl shadow-2xl border border-gray-100 overflow-hidden hidden animate-slide-up">
                    <div class="bg-gradient-to-r from-emerald-500 to-teal-500 p-8">
                        <h3 class="text-3xl font-bold text-white flex items-center">
                            <i class="fas fa-clipboard-check mr-4"></i>
                            Résultats de l'analyse IA
                        </h3>
                        <p class="text-emerald-100 mt-2 text-lg">Analyse complète terminée avec succès</p>
                    </div>
                    <div class="p-8">
                    
                        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                            <!-- Image with Annotations -->
                            <div class="lg:col-span-2">
                                <div class="bg-gradient-to-br from-gray-50 to-gray-100 rounded-2xl p-6 border border-gray-200">
                                    <div class="relative mb-6">
                                        <img id="resultImage" src="" alt="Résultat d'analyse" class="w-full rounded-xl shadow-lg">
                                        <div id="highlightAreas" class="absolute inset-0"></div>
                                    </div>
                                    <div class="flex justify-center space-x-3">
                                        <button id="generateReportBtn" class="bg-gradient-to-r from-blue-500 to-blue-600 text-white px-4 py-2 rounded-full font-medium hover:from-blue-600 hover:to-blue-700 transition-all flex items-center shadow-md">
                                            <i class="fas fa-file-medical mr-2"></i> Générer un rapport
                                        </button>
                                        <button id="shareBtn" class="bg-gradient-to-r from-emerald-500 to-emerald-600 text-white px-4 py-2 rounded-full font-medium hover:from-emerald-600 hover:to-emerald-700 transition-all flex items-center shadow-md">
                                            <i class="fas fa-share-alt mr-2"></i> Partager
                                        </button>
                                        <button class="bg-white text-gray-700 px-4 py-2 rounded-full font-medium border border-gray-300 hover:border-primary-300 hover:text-primary-600 transition-all flex items-center">
                                            <i class="fas fa-download mr-2"></i> Exporter
                                        </button>
                                    </div>
                                </div>
                            </div>
                        
                            <!-- Diagnosis Information -->
                            <div class="space-y-6">
                                <!-- Diagnosis Summary -->
                                <div class="bg-gradient-to-br from-white to-blue-50 border border-blue-200 rounded-2xl p-6 shadow-lg">
                                    <h4 class="text-xl font-bold text-gray-800 mb-4 flex items-center">
                                        <i class="fas fa-stethoscope text-blue-500 mr-3"></i>
                                        Résumé du diagnostic
                                    </h4>
                                    <div class="space-y-4">
                                        <div class="flex items-center justify-between p-3 bg-white rounded-xl">
                                            <span class="font-medium text-gray-700">Probabilité de tumeur:</span>
                                            <span id="tumorProbability" class="text-2xl font-bold text-blue-600">89%</span>
                                        </div>
                                        <div class="flex items-center justify-between p-3 bg-white rounded-xl">
                                            <span class="font-medium text-gray-700">Diagnostic principal:</span>
                                            <span id="mainDiagnosis" class="diagnosis-badge bg-red-100 text-red-800">Suspect</span>
                                        </div>
                                    </div>
                                </div>
                            
                                <!-- Tumor Characteristics -->
                                <div class="bg-gradient-to-br from-white to-purple-50 border border-purple-200 rounded-2xl p-6 shadow-lg">
                                    <h4 class="text-xl font-bold text-gray-800 mb-4 flex items-center">
                                        <i class="fas fa-chart-pie text-purple-500 mr-3"></i>
                                        Classification des tumeurs
                                    </h4>
                                    <div class="space-y-4">
                                        <div class="p-3 bg-white rounded-xl">
                                            <div class="flex justify-between items-center mb-2">
                                                <span class="font-medium text-gray-700">Méningiome</span>
                                                <span id="meningiomaProb" class="font-bold text-blue-600">12%</span>
                                            </div>
                                            <div class="w-full bg-gray-200 rounded-full h-3">
                                                <div class="bg-gradient-to-r from-blue-400 to-blue-500 h-3 rounded-full transition-all duration-1000" style="width: 12%"></div>
                                            </div>
                                        </div>
                                        <div class="p-3 bg-white rounded-xl">
                                            <div class="flex justify-between items-center mb-2">
                                                <span class="font-medium text-gray-700">Gliome</span>
                                                <span id="gliomaProb" class="font-bold text-emerald-600">34%</span>
                                            </div>
                                            <div class="w-full bg-gray-200 rounded-full h-3">
                                                <div class="bg-gradient-to-r from-emerald-400 to-emerald-500 h-3 rounded-full transition-all duration-1000" style="width: 34%"></div>
                                            </div>
                                        </div>
                                        <div class="p-3 bg-white rounded-xl">
                                            <div class="flex justify-between items-center mb-2">
                                                <span class="font-medium text-gray-700">Tumeur pituitaire</span>
                                                <span id="metastasisProb" class="font-bold text-purple-600">43%</span>
                                            </div>
                                            <div class="w-full bg-gray-200 rounded-full h-3">
                                                <div class="bg-gradient-to-r from-purple-400 to-purple-500 h-3 rounded-full transition-all duration-1000" style="width: 43%"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            
                            <!-- Recommendations -->
                            <div class="bg-white border border-gray-200 rounded-lg p-4 shadow-sm">
                                <h4 class="font-medium text-gray-700 mb-3">Recommandations</h4>
                                <ul class="space-y-2 text-sm text-gray-600">
                                    <li class="flex items-start">
                                        <i class="fas fa-check-circle text-green-500 mt-1 mr-2 flex-shrink-0"></i>
                                        <span>Biopsie recommandée pour confirmation histologique</span>
                                    </li>
                                    <li class="flex items-start">
                                        <i class="fas fa-check-circle text-green-500 mt-1 mr-2 flex-shrink-0"></i>
                                        <span>IRM de suivi dans 3 mois pour évaluation de la croissance</span>
                                    </li>
                                    <li class="flex items-start">
                                        <i class="fas fa-check-circle text-green-500 mt-1 mr-2 flex-shrink-0"></i>
                                        <span>Consultation avec un neuro-oncologue spécialisé</span>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-6 flex flex-wrap gap-3">
                        <button class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition flex items-center">
                            <i class="fas fa-file-pdf mr-2"></i> Générer un rapport
                        </button>
                        <button class="bg-white hover:bg-gray-50 text-gray-700 font-medium py-2 px-4 border border-gray-300 rounded-lg transition flex items-center">
                            <i class="fas fa-share-alt mr-2"></i> Partager avec un collègue
                        </button>
                        <button class="bg-white hover:bg-gray-50 text-gray-700 font-medium py-2 px-4 border border-gray-300 rounded-lg transition flex items-center">
                            <i class="fas fa-history mr-2"></i> Comparer avec des précédents
                        </button>
                    </div>
                </div>
            </div>
        </section>
        
        <!-- Information Section -->
        <section class="mb-12">
            <h2 class="text-2xl font-bold text-gray-800 mb-6">Comment fonctionne notre analyse ?</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="bg-white rounded-xl shadow-sm p-6">
                    <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-4">
                        <i class="fas fa-robot text-blue-600"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-800 mb-2">Intelligence Artificielle</h3>
                    <p class="text-gray-600">Notre modèle deep learning a été entraîné sur plus de 10 000 cas validés par des experts en neuroradiologie.</p>
                </div>
                
                <div class="bg-white rounded-xl shadow-sm p-6">
                    <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mb-4">
                        <i class="fas fa-chart-line text-green-600"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-800 mb-2">Métriques quantitatives</h3>
                    <p class="text-gray-600">Analyse précise de la taille, de la forme, de la texture et des caractéristiques de rehaussement de la lésion.</p>
                </div>
                
                <div class="bg-white rounded-xl shadow-sm p-6">
                    <div class="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mb-4">
                        <i class="fas fa-user-md text-purple-600"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-800 mb-2">Support décisionnel</h3>
                    <p class="text-gray-600">Fournit des recommandations basées sur les dernières guidelines cliniques pour chaque cas analysé.</p>
                </div>
            </div>
        </section>
    </main>

    <!-- Modal pour générer un rapport -->
    <div id="reportModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center p-4">
        <div class="bg-white rounded-3xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div class="bg-gradient-to-r from-blue-500 to-blue-600 p-6 rounded-t-3xl">
                <div class="flex justify-between items-center">
                    <h3 class="text-2xl font-bold text-white flex items-center">
                        <i class="fas fa-file-medical mr-3"></i>
                        Générer un rapport médical
                    </h3>
                    <button id="closeReportModal" class="text-white hover:text-gray-200 transition-colors">
                        <i class="fas fa-times text-2xl"></i>
                    </button>
                </div>
            </div>

            <div class="p-8">
                <form id="reportForm">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        <div>
                            <label class="block text-sm font-semibold text-gray-700 mb-2">
                                <i class="fas fa-user mr-2 text-blue-500"></i>Nom du patient
                            </label>
                            <input type="text" id="patientName" class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="Nom complet du patient">
                        </div>
                        <div>
                            <label class="block text-sm font-semibold text-gray-700 mb-2">
                                <i class="fas fa-calendar mr-2 text-blue-500"></i>Date de naissance
                            </label>
                            <input type="date" id="patientDob" class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        </div>
                        <div>
                            <label class="block text-sm font-semibold text-gray-700 mb-2">
                                <i class="fas fa-id-card mr-2 text-blue-500"></i>ID Patient
                            </label>
                            <input type="text" id="patientId" class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="ID unique du patient">
                        </div>
                        <div>
                            <label class="block text-sm font-semibold text-gray-700 mb-2">
                                <i class="fas fa-user-md mr-2 text-blue-500"></i>Médecin référent
                            </label>
                            <input type="text" id="doctorName" class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="Dr. Nom du médecin">
                        </div>
                    </div>

                    <div class="mb-6">
                        <label class="block text-sm font-semibold text-gray-700 mb-2">
                            <i class="fas fa-notes-medical mr-2 text-blue-500"></i>Notes cliniques additionnelles
                        </label>
                        <textarea id="clinicalNotes" rows="4" class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="Observations cliniques, symptômes, historique médical..."></textarea>
                    </div>

                    <div class="mb-6">
                        <label class="block text-sm font-semibold text-gray-700 mb-3">
                            <i class="fas fa-file-alt mr-2 text-blue-500"></i>Format du rapport
                        </label>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <label class="flex items-center p-4 border border-gray-300 rounded-xl cursor-pointer hover:border-blue-500 transition-colors">
                                <input type="radio" name="reportFormat" value="pdf" checked class="mr-3 text-blue-500">
                                <div>
                                    <i class="fas fa-file-pdf text-red-500 text-xl mb-2"></i>
                                    <div class="font-medium">PDF</div>
                                    <div class="text-sm text-gray-500">Format standard</div>
                                </div>
                            </label>
                            <label class="flex items-center p-4 border border-gray-300 rounded-xl cursor-pointer hover:border-blue-500 transition-colors">
                                <input type="radio" name="reportFormat" value="docx" class="mr-3 text-blue-500">
                                <div>
                                    <i class="fas fa-file-word text-blue-600 text-xl mb-2"></i>
                                    <div class="font-medium">DOCX</div>
                                    <div class="text-sm text-gray-500">Éditable</div>
                                </div>
                            </label>
                            <label class="flex items-center p-4 border border-gray-300 rounded-xl cursor-pointer hover:border-blue-500 transition-colors">
                                <input type="radio" name="reportFormat" value="dicom" class="mr-3 text-blue-500">
                                <div>
                                    <i class="fas fa-file-medical text-green-600 text-xl mb-2"></i>
                                    <div class="font-medium">DICOM SR</div>
                                    <div class="text-sm text-gray-500">Standard médical</div>
                                </div>
                            </label>
                        </div>
                    </div>

                    <div class="flex justify-end space-x-4">
                        <button type="button" id="cancelReport" class="px-6 py-3 border border-gray-300 text-gray-700 rounded-xl font-medium hover:bg-gray-50 transition-colors">
                            Annuler
                        </button>
                        <button type="submit" class="px-6 py-3 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-xl font-medium hover:from-blue-600 hover:to-blue-700 transition-all flex items-center">
                            <i class="fas fa-download mr-2"></i>
                            Générer le rapport
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal pour partager avec un collègue -->
    <div id="shareModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center p-4">
        <div class="bg-white rounded-3xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div class="bg-gradient-to-r from-emerald-500 to-emerald-600 p-6 rounded-t-3xl">
                <div class="flex justify-between items-center">
                    <h3 class="text-2xl font-bold text-white flex items-center">
                        <i class="fas fa-share-alt mr-3"></i>
                        Partager avec un collègue
                    </h3>
                    <button id="closeShareModal" class="text-white hover:text-gray-200 transition-colors">
                        <i class="fas fa-times text-2xl"></i>
                    </button>
                </div>
            </div>

            <div class="p-8">
                <form id="shareForm">
                    <div class="mb-6">
                        <label class="block text-sm font-semibold text-gray-700 mb-2">
                            <i class="fas fa-envelope mr-2 text-emerald-500"></i>Email du destinataire
                        </label>
                        <input type="email" id="recipientEmail" class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-emerald-500 focus:border-transparent" placeholder="<EMAIL>">
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        <div>
                            <label class="block text-sm font-semibold text-gray-700 mb-2">
                                <i class="fas fa-user-md mr-2 text-emerald-500"></i>Nom du destinataire
                            </label>
                            <input type="text" id="recipientName" class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-emerald-500 focus:border-transparent" placeholder="Dr. Nom du collègue">
                        </div>
                        <div>
                            <label class="block text-sm font-semibold text-gray-700 mb-2">
                                <i class="fas fa-hospital mr-2 text-emerald-500"></i>Service/Spécialité
                            </label>
                            <select id="recipientSpecialty" class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-emerald-500 focus:border-transparent">
                                <option value="">Sélectionner une spécialité</option>
                                <option value="neurologie">Neurologie</option>
                                <option value="neurochirurgie">Neurochirurgie</option>
                                <option value="radiologie">Radiologie</option>
                                <option value="oncologie">Oncologie</option>
                                <option value="medecine-interne">Médecine interne</option>
                                <option value="autre">Autre</option>
                            </select>
                        </div>
                    </div>

                    <div class="mb-6">
                        <label class="block text-sm font-semibold text-gray-700 mb-2">
                            <i class="fas fa-comment-medical mr-2 text-emerald-500"></i>Message personnel
                        </label>
                        <textarea id="shareMessage" rows="4" class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-emerald-500 focus:border-transparent" placeholder="Bonjour Dr. [Nom], je souhaiterais avoir votre avis sur cette analyse IRM..."></textarea>
                    </div>

                    <div class="mb-6">
                        <label class="block text-sm font-semibold text-gray-700 mb-3">
                            <i class="fas fa-shield-alt mr-2 text-emerald-500"></i>Niveau de confidentialité
                        </label>
                        <div class="space-y-3">
                            <label class="flex items-center p-3 border border-gray-300 rounded-xl cursor-pointer hover:border-emerald-500 transition-colors">
                                <input type="radio" name="confidentiality" value="standard" checked class="mr-3 text-emerald-500">
                                <div>
                                    <div class="font-medium">Standard</div>
                                    <div class="text-sm text-gray-500">Partage sécurisé avec chiffrement</div>
                                </div>
                            </label>
                            <label class="flex items-center p-3 border border-gray-300 rounded-xl cursor-pointer hover:border-emerald-500 transition-colors">
                                <input type="radio" name="confidentiality" value="high" class="mr-3 text-emerald-500">
                                <div>
                                    <div class="font-medium">Élevé</div>
                                    <div class="text-sm text-gray-500">Accès temporaire avec expiration automatique</div>
                                </div>
                            </label>
                        </div>
                    </div>

                    <div class="flex justify-end space-x-4">
                        <button type="button" id="cancelShare" class="px-6 py-3 border border-gray-300 text-gray-700 rounded-xl font-medium hover:bg-gray-50 transition-colors">
                            Annuler
                        </button>
                        <button type="submit" class="px-6 py-3 bg-gradient-to-r from-emerald-500 to-emerald-600 text-white rounded-xl font-medium hover:from-emerald-600 hover:to-emerald-700 transition-all flex items-center">
                            <i class="fas fa-paper-plane mr-2"></i>
                            Envoyer
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Contact Section -->
        <section id="contact" class="py-20 bg-gradient-to-br from-primary-600 via-blue-700 to-indigo-800 text-white">
            <div class="container mx-auto px-4 lg:px-8">
                <div class="max-w-4xl mx-auto text-center">
                    <h3 class="text-4xl lg:text-5xl font-bold mb-6">
                        Prêt à révolutionner vos diagnostics ?
                    </h3>
                    <p class="text-xl text-blue-100 mb-12 max-w-3xl mx-auto">
                        Rejoignez des milliers de professionnels de santé qui font confiance à notre technologie IA
                        pour améliorer leurs diagnostics et soins aux patients.
                    </p>

                    <div class="grid md:grid-cols-3 gap-8 mb-12">
                        <div class="text-center">
                            <div class="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-envelope text-2xl"></i>
                            </div>
                            <h4 class="text-xl font-semibold mb-2">Email</h4>
                            <p class="text-blue-100"><EMAIL></p>
                        </div>

                        <div class="text-center">
                            <div class="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-phone text-2xl"></i>
                            </div>
                            <h4 class="text-xl font-semibold mb-2">Téléphone</h4>
                            <p class="text-blue-100">+33 1 23 45 67 89</p>
                        </div>

                        <div class="text-center">
                            <div class="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-map-marker-alt text-2xl"></i>
                            </div>
                            <h4 class="text-xl font-semibold mb-2">Adresse</h4>
                            <p class="text-blue-100">Paris, France</p>
                        </div>
                    </div>

                    <div class="flex flex-col sm:flex-row gap-4 justify-center">
                        <button class="bg-white text-primary-600 px-8 py-4 rounded-full font-semibold text-lg hover:bg-gray-100 transition-all">
                            <i class="fas fa-calendar-alt mr-2"></i>Demander une démo
                        </button>
                        <button class="border-2 border-white text-white px-8 py-4 rounded-full font-semibold text-lg hover:bg-white hover:text-primary-600 transition-all">
                            <i class="fas fa-download mr-2"></i>Documentation
                        </button>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-16">
        <div class="container mx-auto px-4 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8 mb-12">
                <div class="md:col-span-2">
                    <div class="flex items-center space-x-3 mb-6">
                        <div class="w-12 h-12 medical-gradient rounded-xl flex items-center justify-center">
                            <i class="fas fa-brain text-white text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-2xl font-bold">NeuroScan</h3>
                            <p class="text-gray-400 text-sm">AI Medical Analysis</p>
                        </div>
                    </div>
                    <p class="text-gray-300 mb-6 max-w-md">
                        Révolutionnez le diagnostic médical avec notre technologie d'intelligence artificielle
                        de pointe pour l'analyse des tumeurs cérébrales.
                    </p>
                    <div class="flex space-x-4">
                        <a href="#" class="w-10 h-10 bg-gray-800 rounded-full flex items-center justify-center hover:bg-primary-600 transition-colors">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="#" class="w-10 h-10 bg-gray-800 rounded-full flex items-center justify-center hover:bg-primary-600 transition-colors">
                            <i class="fab fa-linkedin"></i>
                        </a>
                        <a href="#" class="w-10 h-10 bg-gray-800 rounded-full flex items-center justify-center hover:bg-primary-600 transition-colors">
                            <i class="fab fa-github"></i>
                        </a>
                    </div>
                </div>

                <div>
                    <h4 class="text-lg font-semibold mb-4">Produit</h4>
                    <ul class="space-y-2 text-gray-300">
                        <li><a href="#" class="hover:text-white transition-colors">Fonctionnalités</a></li>
                        <li><a href="#" class="hover:text-white transition-colors">Tarifs</a></li>
                        <li><a href="#" class="hover:text-white transition-colors">API</a></li>
                        <li><a href="#" class="hover:text-white transition-colors">Documentation</a></li>
                    </ul>
                </div>

                <div>
                    <h4 class="text-lg font-semibold mb-4">Support</h4>
                    <ul class="space-y-2 text-gray-300">
                        <li><a href="#" class="hover:text-white transition-colors">Centre d'aide</a></li>
                        <li><a href="#" class="hover:text-white transition-colors">Contact</a></li>
                        <li><a href="#" class="hover:text-white transition-colors">Statut</a></li>
                        <li><a href="#" class="hover:text-white transition-colors">Communauté</a></li>
                    </ul>
                </div>
            </div>

            <div class="section-divider mb-8"></div>

            <div class="flex flex-col md:flex-row justify-between items-center">
                <p class="text-gray-400 text-sm mb-4 md:mb-0">
                    © 2025 NeuroScan. Tous droits réservés.
                </p>
                <div class="flex space-x-6 text-sm text-gray-400">
                    <a href="#" class="hover:text-white transition-colors">Politique de confidentialité</a>
                    <a href="#" class="hover:text-white transition-colors">Conditions d'utilisation</a>
                    <a href="#" class="hover:text-white transition-colors">Cookies</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Chatbot médical -->
    <div id="chatbot" class="fixed bottom-6 right-6 z-50">
        <!-- Bouton d'ouverture du chatbot -->
        <button id="chatbotToggle" class="w-16 h-16 bg-gradient-to-r from-blue-500 to-medical-500 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center">
            <i class="fas fa-robot text-2xl"></i>
        </button>

        <!-- Interface du chatbot -->
        <div id="chatbotInterface" class="hidden absolute bottom-20 right-0 w-96 h-[500px] bg-white rounded-2xl shadow-2xl border border-gray-200 flex flex-col">
            <!-- Header du chatbot -->
            <div class="bg-gradient-to-r from-blue-500 to-medical-500 p-4 rounded-t-2xl">
                <div class="flex justify-between items-center">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-white bg-opacity-20 rounded-full flex items-center justify-center mr-3">
                            <i class="fas fa-user-md text-white"></i>
                        </div>
                        <div>
                            <h3 class="text-white font-bold">Dr. NeuroBot</h3>
                            <p class="text-blue-100 text-sm">Assistant médical IA</p>
                        </div>
                    </div>
                    <button id="closeChatbot" class="text-white hover:text-gray-200 transition-colors">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
            </div>

            <!-- Zone de messages -->
            <div id="chatMessages" class="flex-1 p-4 overflow-y-auto space-y-3">
                <div class="flex items-start">
                    <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-2 flex-shrink-0">
                        <i class="fas fa-robot text-blue-600 text-sm"></i>
                    </div>
                    <div class="bg-gray-100 rounded-2xl rounded-tl-sm p-3 max-w-xs">
                        <p class="text-sm text-gray-800">Bonjour ! Je suis Dr. NeuroBot, votre assistant médical spécialisé en neurologie. Comment puis-je vous aider aujourd'hui ?</p>
                    </div>
                </div>
            </div>

            <!-- Zone de saisie -->
            <div class="p-4 border-t border-gray-200">
                <div class="flex space-x-2">
                    <input type="text" id="chatInput" placeholder="Posez votre question médicale..." class="flex-1 px-4 py-2 border border-gray-300 rounded-full focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm">
                    <button id="sendMessage" class="w-10 h-10 bg-gradient-to-r from-blue-500 to-medical-500 text-white rounded-full hover:shadow-lg transition-all flex items-center justify-center">
                        <i class="fas fa-paper-plane text-sm"></i>
                    </button>
                </div>
                <div class="flex justify-center mt-2">
                    <button id="shareAnalysisWithBot" class="text-xs text-blue-600 hover:text-blue-800 transition-colors disabled:text-gray-400 disabled:cursor-not-allowed" disabled>
                        <i class="fas fa-share mr-1"></i>Partager l'analyse actuelle
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // DOM Elements
            const uploadArea = document.getElementById('uploadArea');
            const fileInput = document.getElementById('fileInput');
            const fileInfo = document.getElementById('fileInfo');
            const fileName = document.getElementById('fileName');
            const uploadProgress = document.getElementById('uploadProgress');
            const analyzeBtn = document.getElementById('analyzeBtn');
            const progressCard = document.getElementById('progressCard');
            const resultsCard = document.getElementById('resultsCard');
            const progressPercent = document.getElementById('progressPercent');
            const analysisProgress = document.getElementById('analysisProgress');
            const segmentationIcon = document.getElementById('segmentationIcon');
            const classificationIcon = document.getElementById('classificationIcon');
            const resultImage = document.getElementById('resultImage');
            const highlightAreas = document.getElementById('highlightAreas');
            const tumorProbability = document.getElementById('tumorProbability');
            const mainDiagnosis = document.getElementById('mainDiagnosis');
            const meningiomaProb = document.getElementById('meningiomaProb');
            const gliomaProb = document.getElementById('gliomaProb');
            const metastasisProb = document.getElementById('metastasisProb');
            
            // Click to select file
            uploadArea.addEventListener('click', () => {
                fileInput.click();
            });

            // File selection
            fileInput.addEventListener('change', () => {
                if (fileInput.files.length) {
                    handleFileSelection(fileInput.files[0]);
                }
            });

            // Drag and drop functionality
            uploadArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                uploadArea.classList.add('dragover');
            });

            uploadArea.addEventListener('dragleave', (e) => {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
            });

            uploadArea.addEventListener('drop', (e) => {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
                if (e.dataTransfer.files.length) {
                    handleFileSelection(e.dataTransfer.files[0]);
                }
            });
            
            // Analyze button
            analyzeBtn.addEventListener('click', startAnalysis);

            // Variable globale pour stocker le fichier sélectionné
            let selectedFile = null;

            // Handle file selection
            function handleFileSelection(file) {
                // Validate file type
                const validTypes = ['image/dicom', 'image/nifti', 'image/jpeg', 'image/png', 'application/dicom'];
                const fileExt = file.name.split('.').pop().toLowerCase();

                if (!validTypes.includes(file.type) && !['dcm', 'nii', 'jpg', 'jpeg', 'png'].includes(fileExt)) {
                    alert('Format de fichier non supporté. Veuillez uploader une image IRM (DICOM, NIfTI, JPEG, PNG).');
                    return;
                }

                // Stocker le fichier sélectionné
                selectedFile = file;

                // Display file info
                fileName.textContent = file.name;
                fileInfo.classList.remove('hidden');
                analyzeBtn.disabled = false;

                // Simulate upload progress
                let progress = 0;
                const interval = setInterval(() => {
                    progress += Math.random() * 10;
                    if (progress >= 100) {
                        progress = 100;
                        clearInterval(interval);
                    }
                    uploadProgress.style.width = `${progress}%`;
                }, 200);
            }
            
            // Reset file upload
            function resetFileUpload() {
                fileInput.value = '';
                fileInfo.classList.add('hidden');
                analyzeBtn.disabled = true;
                uploadProgress.style.width = '0%';
                selectedFile = null;
            }
            
            // Start analysis
            function startAnalysis() {
                if (!selectedFile) {
                    alert('Veuillez sélectionner un fichier avant de lancer l\'analyse.');
                    return;
                }

                // Show progress card
                progressCard.classList.remove('hidden');

                // Créer FormData pour l'upload
                const formData = new FormData();
                formData.append('file', selectedFile);

                // Ajouter les informations patient si disponibles
                const patientId = document.getElementById('patientId').value.trim();
                const patientName = document.getElementById('patientName').value.trim();
                const examDate = document.getElementById('examDate').value;

                if (patientId) {
                    formData.append('patient_id', patientId);
                }
                if (patientName) {
                    formData.append('patient_name', patientName);
                }
                if (examDate) {
                    formData.append('exam_date', examDate);
                }

                // Simulate analysis progress
                let progress = 0;
                const progressInterval = setInterval(() => {
                    progress += Math.random() * 5;
                    if (progress >= 90) {
                        progress = 90; // Arrêter à 90% en attendant la réponse du serveur
                    }
                    progressPercent.textContent = `${Math.min(progress, 100).toFixed(0)}%`;
                    analysisProgress.style.width = `${Math.min(progress, 100)}%`;
                }, 200);

                // Envoyer la requête au serveur
                fetch('/upload', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    clearInterval(progressInterval);

                    if (data.success) {
                        // Compléter la barre de progression
                        progressPercent.textContent = '100%';
                        analysisProgress.style.width = '100%';

                        // Update icons when steps complete
                        segmentationIcon.classList.remove('fa-spinner', 'fa-spin');
                        segmentationIcon.classList.add('fa-check');

                        setTimeout(() => {
                            classificationIcon.classList.remove('fa-circle', 'text-gray-300');
                            classificationIcon.classList.add('fa-spinner', 'fa-spin', 'text-blue-600');

                            setTimeout(() => {
                                classificationIcon.classList.remove('fa-spinner', 'fa-spin');
                                classificationIcon.classList.add('fa-check');

                                // Show results after a short delay
                                setTimeout(() => showResults(data), 500);
                            }, 1500);
                        }, 1000);
                    } else {
                        clearInterval(progressInterval);
                        alert('Erreur lors de l\'analyse: ' + (data.error || 'Erreur inconnue'));
                        progressCard.classList.add('hidden');
                    }
                })
                .catch(error => {
                    clearInterval(progressInterval);
                    console.error('Erreur:', error);
                    alert('Erreur lors de l\'analyse: ' + error.message);
                    progressCard.classList.add('hidden');
                });
            }
            
            // Show results
            function showResults(data) {
                // Stocker les données pour les fonctionnalités de rapport et partage
                currentAnalysisData = data;

                // Activer le bouton de partage avec le chatbot
                const shareAnalysisWithBot = document.getElementById('shareAnalysisWithBot');
                if (shareAnalysisWithBot) {
                    shareAnalysisWithBot.disabled = false;
                }

                progressCard.classList.add('hidden');
                resultsCard.classList.remove('hidden');

                // Afficher l'image analysée
                resultImage.src = data.image_url;

                // Déterminer la couleur du diagnostic
                let diagnosisClass = 'diagnosis-badge ';
                if (data.is_tumor) {
                    diagnosisClass += 'bg-red-100 text-red-800';
                } else {
                    diagnosisClass += 'bg-green-100 text-green-800';
                }

                // Mettre à jour les résultats
                tumorProbability.textContent = `${(data.confidence * 100).toFixed(1)}%`;
                mainDiagnosis.textContent = data.prediction;
                mainDiagnosis.className = diagnosisClass;

                // Mettre à jour les probabilités des différents types
                const probs = data.probabilities;
                meningiomaProb.textContent = `${(probs['Méningiome'] * 100).toFixed(1)}%`;
                gliomaProb.textContent = `${(probs['Gliome'] * 100).toFixed(1)}%`;
                metastasisProb.textContent = `${(probs['Tumeur pituitaire'] * 100).toFixed(1)}%`;

                // Mettre à jour les barres de progression
                document.querySelector('#meningiomaProb').parentElement.nextElementSibling.firstElementChild.style.width = `${probs['Méningiome'] * 100}%`;
                document.querySelector('#gliomaProb').parentElement.nextElementSibling.firstElementChild.style.width = `${probs['Gliome'] * 100}%`;
                document.querySelector('#metastasisProb').parentElement.nextElementSibling.firstElementChild.style.width = `${probs['Tumeur pituitaire'] * 100}%`;

                // Mettre à jour la description médicale IA
                const aiDescription = document.getElementById('aiDescription');
                if (data.description) {
                    aiDescription.textContent = data.description;
                } else {
                    aiDescription.textContent = "Description détaillée non disponible pour cette analyse.";
                }

                // Mettre à jour les recommandations
                const recommendationsList = document.querySelector('.space-y-2');
                recommendationsList.innerHTML = '';
                data.recommendations.forEach(rec => {
                    const li = document.createElement('li');
                    li.className = 'flex items-start';
                    li.innerHTML = `
                        <i class="fas fa-check-circle text-green-500 mt-1 mr-2 flex-shrink-0"></i>
                        <span>${rec}</span>
                    `;
                    recommendationsList.appendChild(li);
                });

                // Nettoyer les zones de surbrillance (pas d'annotations automatiques)
                highlightAreas.innerHTML = '';

                // Scroll to results
                resultsCard.scrollIntoView({ behavior: 'smooth' });
            }

            // Mobile menu functionality
            const mobileMenuBtn = document.getElementById('mobileMenuBtn');
            const mobileMenu = document.getElementById('mobileMenu');

            mobileMenuBtn.addEventListener('click', () => {
                mobileMenu.classList.toggle('hidden');
            });

            // Smooth scrolling for navigation links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                        // Close mobile menu if open
                        mobileMenu.classList.add('hidden');
                    }
                });
            });



            // Navbar scroll effect
            window.addEventListener('scroll', () => {
                const navbar = document.querySelector('header');
                if (window.scrollY > 100) {
                    navbar.classList.add('shadow-lg');
                } else {
                    navbar.classList.remove('shadow-lg');
                }
            });

            // Fonctionnalités de rapport et partage
            const reportModal = document.getElementById('reportModal');
            const shareModal = document.getElementById('shareModal');
            const generateReportBtn = document.getElementById('generateReportBtn');
            const shareBtn = document.getElementById('shareBtn');
            const closeReportModal = document.getElementById('closeReportModal');
            const closeShareModal = document.getElementById('closeShareModal');
            const cancelReport = document.getElementById('cancelReport');
            const cancelShare = document.getElementById('cancelShare');
            const reportForm = document.getElementById('reportForm');
            const shareForm = document.getElementById('shareForm');

            // Variable globale pour stocker les données d'analyse
            let currentAnalysisData = null;

            // Ouvrir la modal de rapport
            generateReportBtn?.addEventListener('click', () => {
                if (!currentAnalysisData) {
                    alert('Aucune analyse disponible pour générer un rapport.');
                    return;
                }
                reportModal.classList.remove('hidden');
                document.body.style.overflow = 'hidden';
            });

            // Ouvrir la modal de partage
            shareBtn?.addEventListener('click', () => {
                if (!currentAnalysisData) {
                    alert('Aucune analyse disponible pour partager.');
                    return;
                }
                shareModal.classList.remove('hidden');
                document.body.style.overflow = 'hidden';
            });

            // Fermer les modales
            function closeModal(modal) {
                modal.classList.add('hidden');
                document.body.style.overflow = 'auto';
            }

            closeReportModal?.addEventListener('click', () => closeModal(reportModal));
            closeShareModal?.addEventListener('click', () => closeModal(shareModal));
            cancelReport?.addEventListener('click', () => closeModal(reportModal));
            cancelShare?.addEventListener('click', () => closeModal(shareModal));

            // Fermer les modales en cliquant à l'extérieur
            reportModal?.addEventListener('click', (e) => {
                if (e.target === reportModal) closeModal(reportModal);
            });
            shareModal?.addEventListener('click', (e) => {
                if (e.target === shareModal) closeModal(shareModal);
            });

            // Gérer la soumission du formulaire de rapport
            reportForm?.addEventListener('submit', async (e) => {
                e.preventDefault();

                const formData = {
                    patientName: document.getElementById('patientName').value,
                    patientDob: document.getElementById('patientDob').value,
                    patientId: document.getElementById('patientId').value,
                    doctorName: document.getElementById('doctorName').value,
                    clinicalNotes: document.getElementById('clinicalNotes').value,
                    reportFormat: document.querySelector('input[name="reportFormat"]:checked').value,
                    analysisData: currentAnalysisData
                };

                try {
                    // Simuler la génération du rapport
                    const button = e.target.querySelector('button[type="submit"]');
                    const originalText = button.innerHTML;
                    button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Génération en cours...';
                    button.disabled = true;

                    // Simuler un délai de génération
                    await new Promise(resolve => setTimeout(resolve, 2000));

                    // Créer un lien de téléchargement simulé
                    const reportData = generateReportContent(formData);
                    downloadReport(reportData, formData.reportFormat);

                    button.innerHTML = originalText;
                    button.disabled = false;
                    closeModal(reportModal);

                    // Notification de succès
                    showNotification('Rapport généré avec succès!', 'success');

                } catch (error) {
                    console.error('Erreur lors de la génération du rapport:', error);
                    showNotification('Erreur lors de la génération du rapport', 'error');
                }
            });

            // Gérer la soumission du formulaire de partage
            shareForm?.addEventListener('submit', async (e) => {
                e.preventDefault();

                const formData = {
                    recipientEmail: document.getElementById('recipientEmail').value,
                    recipientName: document.getElementById('recipientName').value,
                    recipientSpecialty: document.getElementById('recipientSpecialty').value,
                    shareMessage: document.getElementById('shareMessage').value,
                    confidentiality: document.querySelector('input[name="confidentiality"]:checked').value,
                    analysisData: currentAnalysisData
                };

                try {
                    // Simuler l'envoi
                    const button = e.target.querySelector('button[type="submit"]');
                    const originalText = button.innerHTML;
                    button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Envoi en cours...';
                    button.disabled = true;

                    // Simuler un délai d'envoi
                    await new Promise(resolve => setTimeout(resolve, 1500));

                    button.innerHTML = originalText;
                    button.disabled = false;
                    closeModal(shareModal);

                    // Notification de succès
                    showNotification(`Analyse partagée avec ${formData.recipientName || formData.recipientEmail}`, 'success');

                } catch (error) {
                    console.error('Erreur lors du partage:', error);
                    showNotification('Erreur lors du partage', 'error');
                }
            });

            // Fonction pour générer le contenu du rapport
            function generateReportContent(formData) {
                const analysisData = formData.analysisData;
                const currentDate = new Date().toLocaleDateString('fr-FR');

                return `
RAPPORT D'ANALYSE IRM - NEUROSCAN
=====================================

INFORMATIONS PATIENT
--------------------
Nom: ${formData.patientName || 'Non spécifié'}
Date de naissance: ${formData.patientDob || 'Non spécifiée'}
ID Patient: ${formData.patientId || 'Non spécifié'}
Médecin référent: ${formData.doctorName || 'Non spécifié'}
Date d'analyse: ${currentDate}

RÉSULTATS DE L'ANALYSE IA
--------------------------
Diagnostic principal: ${analysisData.prediction}
Niveau de confiance: ${(analysisData.confidence * 100).toFixed(1)}%
Tumeur détectée: ${analysisData.is_tumor ? 'Oui' : 'Non'}

PROBABILITÉS DÉTAILLÉES
------------------------
- Normal: ${(analysisData.probabilities.Normal * 100).toFixed(1)}%
- Gliome: ${(analysisData.probabilities.Gliome * 100).toFixed(1)}%
- Méningiome: ${(analysisData.probabilities.Méningiome * 100).toFixed(1)}%
- Tumeur pituitaire: ${(analysisData.probabilities['Tumeur pituitaire'] * 100).toFixed(1)}%

RECOMMANDATIONS CLINIQUES
--------------------------
${analysisData.recommendations.map(rec => `- ${rec}`).join('\n')}

NOTES CLINIQUES ADDITIONNELLES
-------------------------------
${formData.clinicalNotes || 'Aucune note additionnelle'}

AVERTISSEMENT
-------------
Cette analyse a été générée par un système d'intelligence artificielle à des fins d'aide au diagnostic.
Elle ne remplace pas l'expertise médicale et doit être interprétée par un professionnel de santé qualifié.

Rapport généré par NeuroScan AI - ${currentDate}
                `;
            }

            // Fonction pour télécharger le rapport
            function downloadReport(content, format) {
                const blob = new Blob([content], { type: 'text/plain' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `rapport_neuroscan_${new Date().toISOString().split('T')[0]}.${format === 'pdf' ? 'txt' : format}`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
            }

            // Fonction pour afficher les notifications
            function showNotification(message, type = 'info') {
                const notification = document.createElement('div');
                notification.className = `fixed top-4 right-4 z-50 p-4 rounded-xl shadow-lg transition-all duration-300 transform translate-x-full`;

                if (type === 'success') {
                    notification.className += ' bg-green-500 text-white';
                    notification.innerHTML = `<i class="fas fa-check-circle mr-2"></i>${message}`;
                } else if (type === 'error') {
                    notification.className += ' bg-red-500 text-white';
                    notification.innerHTML = `<i class="fas fa-exclamation-circle mr-2"></i>${message}`;
                } else {
                    notification.className += ' bg-blue-500 text-white';
                    notification.innerHTML = `<i class="fas fa-info-circle mr-2"></i>${message}`;
                }

                document.body.appendChild(notification);

                // Animation d'entrée
                setTimeout(() => {
                    notification.classList.remove('translate-x-full');
                }, 100);

                // Suppression automatique après 3 secondes
                setTimeout(() => {
                    notification.classList.add('translate-x-full');
                    setTimeout(() => {
                        document.body.removeChild(notification);
                    }, 300);
                }, 3000);
            }

            // Fonctionnalités du chatbot médical
            const chatbotToggle = document.getElementById('chatbotToggle');
            const chatbotInterface = document.getElementById('chatbotInterface');
            const closeChatbot = document.getElementById('closeChatbot');
            const chatMessages = document.getElementById('chatMessages');
            const chatInput = document.getElementById('chatInput');
            const sendMessage = document.getElementById('sendMessage');
            const shareAnalysisWithBot = document.getElementById('shareAnalysisWithBot');

            let isChatbotOpen = false;

            // Ouvrir/fermer le chatbot
            chatbotToggle?.addEventListener('click', () => {
                if (isChatbotOpen) {
                    closeChatbotInterface();
                } else {
                    openChatbotInterface();
                }
            });

            closeChatbot?.addEventListener('click', closeChatbotInterface);

            function openChatbotInterface() {
                chatbotInterface.classList.remove('hidden');
                chatbotToggle.innerHTML = '<i class="fas fa-times text-2xl"></i>';
                isChatbotOpen = true;
                chatInput.focus();
            }

            function closeChatbotInterface() {
                chatbotInterface.classList.add('hidden');
                chatbotToggle.innerHTML = '<i class="fas fa-robot text-2xl"></i>';
                isChatbotOpen = false;
            }

            // Envoyer un message
            sendMessage?.addEventListener('click', sendChatMessage);
            chatInput?.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    sendChatMessage();
                }
            });

            async function sendChatMessage() {
                const message = chatInput.value.trim();
                if (!message) return;

                // Ajouter le message de l'utilisateur
                addChatMessage(message, 'user');
                chatInput.value = '';

                // Afficher l'indicateur de frappe
                const typingIndicator = addTypingIndicator();

                try {
                    // Envoyer à l'API Gemini
                    const response = await fetch('/chat', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            message: message,
                            context: 'medical_consultation'
                        })
                    });

                    const data = await response.json();

                    // Supprimer l'indicateur de frappe
                    typingIndicator.remove();

                    if (data.success) {
                        addChatMessage(data.response, 'bot');
                    } else {
                        addChatMessage('Désolé, je rencontre un problème technique. Veuillez réessayer.', 'bot');
                    }

                } catch (error) {
                    console.error('Erreur chatbot:', error);
                    typingIndicator.remove();
                    addChatMessage('Désolé, je ne peux pas répondre pour le moment. Veuillez réessayer plus tard.', 'bot');
                }
            }

            function addChatMessage(message, sender) {
                const messageDiv = document.createElement('div');
                messageDiv.className = 'flex items-start';

                if (sender === 'user') {
                    messageDiv.innerHTML = `
                        <div class="flex-1"></div>
                        <div class="bg-gradient-to-r from-blue-500 to-medical-500 text-white rounded-2xl rounded-tr-sm p-3 max-w-xs ml-8">
                            <p class="text-sm">${message}</p>
                        </div>
                        <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center ml-2 flex-shrink-0">
                            <i class="fas fa-user text-white text-sm"></i>
                        </div>
                    `;
                } else {
                    messageDiv.innerHTML = `
                        <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-2 flex-shrink-0">
                            <i class="fas fa-robot text-blue-600 text-sm"></i>
                        </div>
                        <div class="bg-gray-100 rounded-2xl rounded-tl-sm p-3 max-w-xs">
                            <p class="text-sm text-gray-800">${message}</p>
                        </div>
                    `;
                }

                chatMessages.appendChild(messageDiv);
                chatMessages.scrollTop = chatMessages.scrollHeight;
            }

            function addTypingIndicator() {
                const typingDiv = document.createElement('div');
                typingDiv.className = 'flex items-start';
                typingDiv.innerHTML = `
                    <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-2 flex-shrink-0">
                        <i class="fas fa-robot text-blue-600 text-sm"></i>
                    </div>
                    <div class="bg-gray-100 rounded-2xl rounded-tl-sm p-3">
                        <div class="flex space-x-1">
                            <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                            <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.1s"></div>
                            <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
                        </div>
                    </div>
                `;

                chatMessages.appendChild(typingDiv);
                chatMessages.scrollTop = chatMessages.scrollHeight;
                return typingDiv;
            }

            // Partager l'analyse avec le chatbot
            shareAnalysisWithBot?.addEventListener('click', async () => {
                if (!currentAnalysisData) {
                    showNotification('Aucune analyse disponible à partager', 'error');
                    return;
                }

                const analysisText = `Voici les résultats de mon analyse IRM :
- Diagnostic: ${currentAnalysisData.prediction}
- Confiance: ${(currentAnalysisData.confidence * 100).toFixed(1)}%
- Tumeur détectée: ${currentAnalysisData.is_tumor ? 'Oui' : 'Non'}
- Probabilités: Normal ${(currentAnalysisData.probabilities.Normal * 100).toFixed(1)}%, Gliome ${(currentAnalysisData.probabilities.Gliome * 100).toFixed(1)}%, Méningiome ${(currentAnalysisData.probabilities.Méningiome * 100).toFixed(1)}%, Tumeur pituitaire ${(currentAnalysisData.probabilities['Tumeur pituitaire'] * 100).toFixed(1)}%

Pouvez-vous m'expliquer ces résultats et me donner votre avis médical ?`;

                if (!isChatbotOpen) {
                    openChatbotInterface();
                }

                chatInput.value = analysisText;
                sendChatMessage();
            });
        });

        // Toggle patient information visibility
        function togglePatientInfo() {
            const patientInfo = document.getElementById('patientInfo');
            const showBtn = document.getElementById('showPatientInfoBtn');

            if (patientInfo.classList.contains('hidden')) {
                patientInfo.classList.remove('hidden');
                showBtn.style.display = 'none';
                // Set default exam date to today
                const today = new Date().toISOString().split('T')[0];
                document.getElementById('examDate').value = today;
            } else {
                patientInfo.classList.add('hidden');
                showBtn.style.display = 'block';
                // Clear form fields
                document.getElementById('patientId').value = '';
                document.getElementById('patientName').value = '';
                document.getElementById('examDate').value = '';
            }
        }
    </script>
</body>
</html>